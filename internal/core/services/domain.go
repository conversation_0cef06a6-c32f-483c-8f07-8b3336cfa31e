package services

import (
	"errors"
	"fmt"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"os"
)

type DomainService struct {
	domainRepo         ports.DomainRepository
	namespaceRepo      ports.NamespaceRepository
	clusterRepo        ports.ClusterRepository
	workspaceRepo      ports.WorkspaceRepository
	orderNamespaceRepo ports.OrderNamespaceRepository
	IngressSpecService ports.IngressSpecService
	DnsService         ports.DnsService
	OperationService   ports.OperationService
	OrderDomainService ports.OrderDomainService
	CloudflareService  ports.CloudflareService
}

func NewDomainService(
	domainRepo ports.DomainRepository,
	namespaceRepo ports.NamespaceRepository,
	clusterRepo ports.ClusterRepository,
	workspaceRepo ports.WorkspaceRepository,
	orderNamespaceRepo ports.OrderNamespaceRepository,
	ingressSpecService ports.IngressSpecService,
	dnsService ports.DnsService,
	operationService ports.OperationService,
	orderDomainService ports.OrderDomainService,
	cloudflareService ports.CloudflareService,
) ports.DomainService {
	return &DomainService{
		domainRepo:         domainRepo,
		namespaceRepo:      namespaceRepo,
		clusterRepo:        clusterRepo,
		workspaceRepo:      workspaceRepo,
		orderNamespaceRepo: orderNamespaceRepo,
		IngressSpecService: ingressSpecService,
		DnsService:         dnsService,
		OperationService:   operationService,
		OrderDomainService: orderDomainService,
		CloudflareService:  cloudflareService,
	}
}

func (s *DomainService) Create(name string, isDefault, isActive bool, zoneID, accountID, accountName string, namespaceID uint64) (*domain.Domain, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if zoneID == "" {
		return nil, errors.New("zone ID is required")
	}
	if accountID == "" {
		return nil, errors.New("account ID is required")
	}
	if accountName == "" {
		return nil, errors.New("account name is required")
	}
	if namespaceID == 0 {
		return nil, errors.New("namespace ID is required")
	}

	// Verify namespace exists
	_, err := s.namespaceRepo.FindByID(namespaceID)
	if err != nil {
		return nil, errors.New("namespace not found")
	}

	// Calculate the next index for this namespace
	filter := &ports.DomainFilter{
		NamespaceID: &namespaceID,
	}
	existingDomains, err := s.domainRepo.FindAll(filter)
	if err != nil {
		return nil, err
	}

	// Find the maximum index value
	maxIndex := 0
	for _, d := range existingDomains {
		if d.Index > maxIndex {
			maxIndex = d.Index
		}
	}

	// Set the new dns's index to max + 1
	nextIndex := maxIndex + 1

	domain := &domain.Domain{
		Name:        name,
		IsDefault:   isDefault,
		IsActive:    isActive,
		ZoneID:      zoneID,
		AccountID:   accountID,
		AccountName: accountName,
		NamespaceID: namespaceID,
		Index:       nextIndex,
	}

	err = s.domainRepo.Insert(domain)
	if err != nil {
		return nil, err
	}

	_, err = s.CloudflareService.EnableAlwaysUseHTTPS(zoneID)
	if err != nil {
		return nil, err
	}

	// Automated workflow: Update related order domains availability when domain is created
	go s.updateRelatedOrderDomainsAvailability(domain.NamespaceID, domain.Name)

	return domain, nil
}

func (s *DomainService) GetAll(filter *ports.DomainFilter) ([]*domain.Domain, error) {
	return s.domainRepo.FindAll(filter)
}

func (s *DomainService) GetAllWithPagination(filter *ports.DomainFilter) ([]*domain.Domain, uint64, error) {
	return s.domainRepo.FindAllWithPagination(filter)
}

func (s *DomainService) GetByID(id uint64) (*domain.Domain, error) {
	return s.domainRepo.FindByID(id)
}

func (s *DomainService) GetDefaultByNamespaceID(namespaceID uint64) (*domain.Domain, error) {
	if namespaceID == 0 {
		return nil, errors.New("namespace ID is required")
	}

	// Verify namespace exists
	_, err := s.namespaceRepo.FindByID(namespaceID)
	if err != nil {
		return nil, errors.New("namespace not found")
	}

	// Create filter to find default domain in the namespace
	isDefault := true
	filter := &ports.DomainFilter{
		NamespaceID: &namespaceID,
		IsDefault:   &isDefault,
	}

	domains, err := s.domainRepo.FindAll(filter)
	if err != nil {
		return nil, err
	}

	if len(domains) == 0 {
		return nil, errors.New("no default domain found for this namespace")
	}

	// Return the first (and should be only) default domain
	return domains[0], nil
}

func (s *DomainService) Update(id uint64, name string, isDefault, isActive bool, zoneID, accountID, accountName string, namespaceID uint64, index int) (*domain.Domain, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if zoneID == "" {
		return nil, errors.New("zone ID is required")
	}
	if accountID == "" {
		return nil, errors.New("account ID is required")
	}
	if accountName == "" {
		return nil, errors.New("account name is required")
	}
	if namespaceID == 0 {
		return nil, errors.New("namespace ID is required")
	}

	// Verify namespace exists
	_, err := s.namespaceRepo.FindByID(namespaceID)
	if err != nil {
		return nil, errors.New("namespace not found")
	}

	// Find existing dns
	domain, err := s.domainRepo.FindByID(id)
	if err != nil {
		return nil, err
	}

	// Update fields
	domain.Name = name
	domain.IsDefault = isDefault
	domain.IsActive = isActive
	domain.ZoneID = zoneID
	domain.AccountID = accountID
	domain.AccountName = accountName
	domain.NamespaceID = namespaceID
	domain.Index = index

	err = s.domainRepo.Update(domain)
	if err != nil {
		return nil, err
	}

	return domain, nil
}

func (s *DomainService) UpdateStatus(id uint64, isActive bool) (*domain.Domain, error) {
	// Find existing dns
	domain, err := s.domainRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("dns not found")
	}

	// Update only the is_active field
	domain.IsActive = isActive

	err = s.domainRepo.Update(domain)
	if err != nil {
		return nil, err
	}

	return domain, nil
}

func (s *DomainService) SetDefault(userID, id uint64, accessToken string, isRedirect bool) (*domain.Domain, error) {
	// Find the target dns
	targetDomain, err := s.domainRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("dns not found")
	}

	currentDefaultDomain, err := s.GetDefaultByNamespaceID(targetDomain.NamespaceID)
	if err != nil {
		return nil, err
	}

	masterDomain := os.Getenv("CLOUDFLARE_MASTER_ZONE_NAME")
	isMasterDefault := currentDefaultDomain.Name == masterDomain

	// Get all domains in the same namespace
	filter := &ports.DomainFilter{
		NamespaceID: &targetDomain.NamespaceID,
	}
	domainsInNamespace, err := s.domainRepo.FindAll(filter)
	if err != nil {
		return nil, err
	}

	// Set is_default = false for all other domains in the namespace
	for _, d := range domainsInNamespace {
		if d.ID != id && d.IsDefault {
			d.IsDefault = false
			err = s.domainRepo.Update(d)
			if err != nil {
				return nil, err
			}
		}
	}

	// Set is_default = true for the target dns
	targetDomain.IsDefault = true
	err = s.domainRepo.Update(targetDomain)
	if err != nil {
		return nil, err
	}

	// Update all ingress-specs in the namespace to use the new default dns
	_, err = s.IngressSpecService.UpdateHostsByNamespace(targetDomain.NamespaceID, targetDomain.Name)
	if err != nil {
		return nil, err
	}

	_, err = s.DnsService.HandleDnsAsync(accessToken, dto.HandleDnsRequest{
		NamespaceID: targetDomain.NamespaceID,
		ZoneID:      targetDomain.ZoneID,
		Method:      "apply",
	})

	if err != nil {
		return nil, err
	}

	namespace, err := s.namespaceRepo.FindByID(targetDomain.NamespaceID)
	if err != nil {
		return nil, errors.New("namespace not found")
	}

	var req dto.OperationCreateReq
	req = dto.OperationCreateReq{
		ClusterID:   namespace.ClusterID,
		NamespaceID: targetDomain.NamespaceID,
		Method:      "apply",
	}

	operation, err := s.OperationService.CreateOperationAsync(userID, accessToken, req)
	if err != nil {
		return nil, err
	}
	fmt.Printf("update operation starter : %v\n", operation)

	// Delete Page rule
	if !isMasterDefault {
		pageRules, err := s.CloudflareService.ListPageRules(currentDefaultDomain.ZoneID)
		if err != nil {
			return nil, err
		}
		for _, pageRule := range pageRules.Result {
			_, err := s.CloudflareService.DeletePageRule(currentDefaultDomain.ZoneID, pageRule.ID)
			if err != nil {
				return nil, err
			}
		}
	}

	// Create Page rule
	if isRedirect {
		// remove rule all in target
		pageRules, err := s.CloudflareService.ListPageRules(targetDomain.ZoneID)
		if err != nil {
			return nil, err
		}
		for _, pageRule := range pageRules.Result {
			_, err := s.CloudflareService.DeletePageRule(targetDomain.ZoneID, pageRule.ID)
			if err != nil {
				return nil, err
			}
		}
		// without subdomain
		request := &dto.CloudflarePageRuleCreateRequest{
			Targets: []dto.CloudflarePageRuleTarget{
				{
					Target: "url",
					Constraint: map[string]interface{}{
						"value":    fmt.Sprintf("%s/*", currentDefaultDomain.Name),
						"operator": "matches",
					},
				},
			},
			Actions: []dto.CloudflarePageRuleAction{
				{
					ID: "forwarding_url",
					Value: map[string]interface{}{
						"url":         fmt.Sprintf("https://%s/$1", targetDomain.Name),
						"status_code": 301,
					},
				},
			},
			Status: "active",
		}
		_, err = s.CloudflareService.CreatePageRule(currentDefaultDomain.ZoneID, request)
		if err != nil {
			return nil, err
		}

		// www subdomain
		request2 := &dto.CloudflarePageRuleCreateRequest{
			Targets: []dto.CloudflarePageRuleTarget{
				{
					Target: "url",
					Constraint: map[string]interface{}{
						"value":    fmt.Sprintf("www.%s/*", currentDefaultDomain.Name),
						"operator": "matches",
					},
				},
			},
			Actions: []dto.CloudflarePageRuleAction{
				{
					ID: "forwarding_url",
					Value: map[string]interface{}{
						"url":         fmt.Sprintf("https://www.%s/$1", targetDomain.Name),
						"status_code": 301,
					},
				},
			},
			Status: "active",
		}
		_, err = s.CloudflareService.CreatePageRule(currentDefaultDomain.ZoneID, request2)
		if err != nil {
			return nil, err
		}

		fmt.Printf("Page rule created for %s\n", targetDomain.Name)
	}

	return targetDomain, nil
}

func (s *DomainService) Delete(id uint64) error {
	// First, retrieve the domain to get namespace ID and name before deletion
	domain, err := s.domainRepo.FindByID(id)
	if err != nil {
		return err
	}

	// Perform the deletion
	err = s.domainRepo.Delete(id)
	if err != nil {
		return err
	}

	// After successful deletion, update related order domains availability to false
	// This runs asynchronously to avoid blocking the deletion response
	go s.updateRelatedOrderDomainsAvailabilityOnDelete(domain.NamespaceID, domain.Name)

	return nil
}

func (s *DomainService) DeleteAllByNamespaceID(namespaceID uint64) error {
	if namespaceID == 0 {
		return errors.New("namespace ID is required")
	}

	// Verify namespace exists
	_, err := s.namespaceRepo.FindByID(namespaceID)
	if err != nil {
		return errors.New("namespace not found")
	}

	// Get all domains in the namespace before deletion to update related order domains
	filter := &ports.DomainFilter{
		NamespaceID: &namespaceID,
	}
	domains, err := s.domainRepo.FindAll(filter)
	if err != nil {
		return err
	}

	// Delete all domains in the namespace using a direct database query for efficiency
	// This is similar to how namespace deletion handles cascading deletes
	err = s.domainRepo.DeleteByNamespaceID(namespaceID)
	if err != nil {
		return err
	}

	// After successful deletion, update related order domains availability to false for each deleted domain
	// This runs asynchronously to avoid blocking the deletion response
	for _, domain := range domains {
		go s.updateRelatedOrderDomainsAvailabilityOnDelete(domain.NamespaceID, domain.Name)
	}

	return nil
}

// updateRelatedOrderDomainsAvailability updates the availability status of related order domains
// when a new domain is created. This runs asynchronously to avoid blocking domain creation.
func (s *DomainService) updateRelatedOrderDomainsAvailability(namespaceID uint64, domainName string) {
	// Get related order domains using the namespace ID and domain name
	relatedOrderDomains, err := s.OrderDomainService.GetByNamespaceDomain(namespaceID, domainName)
	if err != nil {
		// Log error but don't fail the domain creation process
		fmt.Printf("Error retrieving related order domains for namespace %d and domain %s: %v\n", namespaceID, domainName, err)
		return
	}

	// Update availability status to true for each related order domain
	for _, orderDomain := range relatedOrderDomains {
		_, err := s.OrderDomainService.UpdateAvailability(orderDomain.ID, true)
		if err != nil {
			// Log error but continue processing other order domains
			fmt.Printf("Error updating availability for order domain %d: %v\n", orderDomain.ID, err)
			continue
		}
		fmt.Printf("Successfully updated availability to true for order domain %d (name: %s)\n", orderDomain.ID, orderDomain.Name)
	}

	if len(relatedOrderDomains) > 0 {
		fmt.Printf("Completed automated workflow: updated %d related order domains for namespace %d and domain %s\n", len(relatedOrderDomains), namespaceID, domainName)
	}
}

// updateRelatedOrderDomainsAvailabilityOnDelete updates the availability status of related order domains
// to false when a domain is deleted. This runs asynchronously to avoid blocking domain deletion.
func (s *DomainService) updateRelatedOrderDomainsAvailabilityOnDelete(namespaceID uint64, domainName string) {
	// Get related order domains using the namespace ID and domain name
	relatedOrderDomains, err := s.OrderDomainService.GetByNamespaceDomain(namespaceID, domainName)
	if err != nil {
		// Log error but don't fail the domain deletion process
		fmt.Printf("Error retrieving related order domains for namespace %d and domain %s during deletion: %v\n", namespaceID, domainName, err)
		return
	}

	// Update availability status to false for each related order domain
	for _, orderDomain := range relatedOrderDomains {
		_, err := s.OrderDomainService.UpdateAvailability(orderDomain.ID, false)
		if err != nil {
			// Log error but continue processing other order domains
			fmt.Printf("Error updating availability to false for order domain %d during deletion: %v\n", orderDomain.ID, err)
			continue
		}
		fmt.Printf("Successfully updated availability to false for order domain %d (name: %s) after domain deletion\n", orderDomain.ID, orderDomain.Name)
	}

	if len(relatedOrderDomains) > 0 {
		fmt.Printf("Completed automated workflow on deletion: updated %d related order domains to unavailable for namespace %d and domain %s\n", len(relatedOrderDomains), namespaceID, domainName)
	}
}

func (s *DomainService) HandleDomainThenChangeNamespaceStatus(userID, namespaceID uint64, accessToken string) error {
	// Get all domains in the same namespace
	isDefault := true
	filter := &ports.DomainFilter{
		NamespaceID: &namespaceID,
		IsDefault:   &isDefault,
	}
	defaultDomains, err := s.domainRepo.FindAll(filter)
	if err != nil {
		return err
	}
	if len(defaultDomains) == 0 {
		return errors.New("no default domain found for this namespace")
	}
	defaultDomain := defaultDomains[0]
	if defaultDomain.AccountName == "master-account" {
		namespace, err := s.namespaceRepo.FindByID(namespaceID)
		if err != nil {
			return errors.New("namespace not found")
		}
		var req dto.OperationCreateReq
		req = dto.OperationCreateReq{
			ClusterID:   namespace.ClusterID,
			NamespaceID: defaultDomain.NamespaceID,
			Method:      "apply",
		}
		operation, err := s.OperationService.CreateOperationAsync(userID, accessToken, req)
		if err != nil {
			return err
		}
		fmt.Printf("update operation starter : %v\n", operation)
	} else {
		_, err = s.DnsService.HandleDnsAsync(accessToken, dto.HandleDnsRequest{
			NamespaceID: defaultDomain.NamespaceID,
			ZoneID:      defaultDomain.ZoneID,
			Method:      "apply",
		})
		if err != nil {
			return err
		}
	}

	return nil
}

// GetUserIDFromNamespaceID finds the user ID associated with a namespace ID
// It tries two approaches:
// 1. Through OrderNamespace relationship (namespace -> order -> user)
// 2. Through Workspace relationship (namespace -> cluster -> workspace -> user)
func (s *DomainService) GetUserIDFromNamespaceID(namespaceID uint64) (uint64, error) {
	if namespaceID == 0 {
		return 0, errors.New("namespace ID is required")
	}

	// First approach: Try to find user through OrderNamespace relationship
	orderNamespaceFilter := &ports.OrderNamespaceFilter{
		NamespaceID: &namespaceID,
	}
	orderNamespaces, err := s.orderNamespaceRepo.FindAll(orderNamespaceFilter)
	if err == nil && len(orderNamespaces) > 0 {
		// Found order namespace, get user ID from the order
		orderNamespace := orderNamespaces[0]
		if orderNamespace.Order != nil && orderNamespace.Order.User != nil {
			return orderNamespace.Order.User.ID, nil
		}
	}

	// Second approach: Try to find user through Workspace relationship
	// Get namespace with cluster information
	namespace, err := s.namespaceRepo.FindByID(namespaceID)
	if err != nil {
		return 0, errors.New("namespace not found")
	}

	// Get cluster with workspace information
	cluster, err := s.clusterRepo.FindByID(namespace.ClusterID, 0)
	if err != nil {
		return 0, errors.New("cluster not found")
	}

	// Get workspace with user information
	workspace, err := s.workspaceRepo.FindByID(cluster.WorkspaceID)
	if err != nil {
		return 0, errors.New("workspace not found")
	}

	if workspace.User == nil {
		return 0, errors.New("user not found in workspace")
	}

	return workspace.User.ID, nil
}
